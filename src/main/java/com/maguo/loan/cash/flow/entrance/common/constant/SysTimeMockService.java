package com.maguo.loan.cash.flow.entrance.common.constant;

import com.jinghang.capital.api.dto.BankChannel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class SysTimeMockService {
    @Value("${cy.mock.switch}")
    private String cyMockSwitch;
    @Value("${cy.mock.time}")
    private String cyMockTime;
    @Value("${hx.mock.switch}")
    private String hxMockSwitch;
    @Value("${hx.mock.time}")
    private String hxMockTime;

    public LocalDateTime mockTime(LocalDateTime time, BankChannel bankChannel) {
        if (bankChannel == null) {
            return time;
        }
        if (bankChannel.equals(BankChannel.CYBK)) {
            return "true".equals(cyMockSwitch) ? LocalDateTime.parse(cyMockTime + "000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) : time;
        } else if (bankChannel.equals(BankChannel.HXBK)) {
            return "true".equals(hxMockSwitch) ? LocalDateTime.parse(hxMockTime + "000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) : time;
        } else {
            return time;
        }
    }
}
