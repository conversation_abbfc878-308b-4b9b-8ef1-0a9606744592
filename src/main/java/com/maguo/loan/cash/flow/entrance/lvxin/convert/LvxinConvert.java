package com.maguo.loan.cash.flow.entrance.lvxin.convert;


import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.convert.ConvertMappings;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.entity.AgreementShow;
import com.maguo.loan.cash.flow.entity.LvxinApplyRecord;
import com.maguo.loan.cash.flow.entity.LvxinBankList;
import com.maguo.loan.cash.flow.entity.LvxinRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.LvxinBankInfo;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.contract.ContractResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ApprovalRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ContactInfo;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.loan.LvxinRepayPlan;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayTrialResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.repay.LvxinRepayTrialResponseV2;
import com.maguo.loan.cash.flow.entrance.lvxin.enums.LvxinLoanStatus;
import com.maguo.loan.cash.flow.enums.DictEnum;
import com.maguo.loan.cash.flow.enums.Education;
import com.maguo.loan.cash.flow.enums.Gender;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.Marriage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.Position;
import com.maguo.loan.cash.flow.enums.Relation;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.RepayPlanItem;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(imports = {LocalDateTime.class, Marriage.class, Industry.class, Relation.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = ConvertMappings.class)
public interface LvxinConvert {

    LvxinConvert INSTANCE = Mappers.getMapper(LvxinConvert.class);
    int CERT_NO_CITY_LENGTH = 4;
    int CERT_NO_DISTRICT_LENGTH = 6;
    int THOUSAND = 1000;


    @Mapping(source = "partnerUserId", target = "orderNo")
    @Mapping(constant = "LVXIN", target = "flowChannel")
    @Mapping(source = "userId", target = "openId")
    @Mapping(source = "name", target = "name")
    @Mapping(source = "phone", target = "mobile")
    @Mapping(source = "idCard", target = "certNo")
    @Mapping(expression = "java(LocalDateTime.now())", target = "applyTime")
    @Mapping(constant = "INIT", target = "preOrderState")
    @Mapping(constant = "N", target = "isReject")
    @Mapping(constant = "SINGLE", target = "amountType")
    @Mapping(source = "creditAmount", target = "applyAmount")
    @Mapping(source = "applyPeriod", target = "applyPeriods")
    @Mapping(constant = "01", target = "applyChannel")
    @Mapping(constant = "Y", target = "isAssignBankChannel")
    @Mapping(source = "productType", target = "bankChannel", qualifiedByName = "toBankChannel")
    @Mapping(source = "productType", target = "isIncludingEquity", qualifiedByName = "toIncludingEquityStr")
    @Mapping(constant = "O", target = "equityRecipient")
    PreOrder toPreOrder(@MappingTarget PreOrder preOrder, ApprovalRequest approvalRequest);

    @Mapping(source = "partnerUserId", target = "orderNo")
    @Mapping(source = "baseInfo.email", target = "email")
    @Mapping(source = "name", target = "name")
    @Mapping(source = "phone", target = "mobile")
    @Mapping(source = "idCard", target = "idCardNo")
    @Mapping(source = "authInfo.frontUrl", target = "idPositive")
    @Mapping(source = "authInfo.backUrl", target = "idNegative")
    @Mapping(source = "authInfo.sex", target = "idSex")
    @Mapping(source = "authInfo.nation", target = "idEthnic")
    @Mapping(source = "authInfo.authority", target = "idIssueOrg")
    @Mapping(source = "authInfo.startDueTimeOcr", target = "idStartTime")
    @Mapping(source = "authInfo.endDueTimeOcr", target = "idEndTime")
    @Mapping(source = "authInfo.address", target = "idAddress")
    @Mapping(source = "authInfo.borrower", target = "livePhoto")
    @Mapping(source = "authInfo.liveRate", target = "faceScore")
    @Mapping(source = "authInfo.facialSupplier", target = "facialSupplier")
    @Mapping(source = "baseInfo.loanPurpose", target = "loanPurpose")
    @Mapping(source = "baseInfo.educational", target = "education")
    @Mapping(source = "baseInfo.inCome", target = "monthlyIncome")
    @Mapping(source = "baseInfo.workType", target = "job")
    @Mapping(source = "baseInfo.companyName", target = "workUnitName", qualifiedByName = "toCompanyName")
    @Mapping(source = "baseInfo.companyAddress", target = "workUnitAddress")
    @Mapping(source = "baseInfo.companyProvinceCode", target = "workUnitProvinceCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.companyCityCode", target = "workUnitCityCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.companyAreaCode", target = "workUnitAreaCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.maritalStatus", target = "marriage")
    @Mapping(source = "deviceInfo.gpsLng", target = "longitude")
    @Mapping(source = "deviceInfo.gpsLat", target = "latitude")
    @Mapping(source = "baseInfo.industry", target = "industry")
    @Mapping(source = "contactInfos", target = "relations", qualifiedByName = "toRelationJsonArrayStr")
    @Mapping(source = "deviceInfo", target = "deviceInfo", qualifiedByName = "toJsonStr")
    @Mapping(source = "baseInfo.liveAddress", target = "livingAddress")
    @Mapping(source = "baseInfo.liveProvince", target = "livingProvince")
    @Mapping(source = "baseInfo.liveCity", target = "livingCity")
    @Mapping(source = "baseInfo.liveArea", target = "livingArea")
    @Mapping(source = "baseInfo.provinceCode", target = "livingProvinceCode")
    @Mapping(source = "baseInfo.cityCode", target = "livingCityCode")
    @Mapping(source = "baseInfo.areaCode", target = "livingAreaCode")
    @Mapping(source = "productType", target = "isIncludingEquity", qualifiedByName = "toIncludingEquityStr")
    @Mapping(constant = "O", target = "equityRecipient")
    LvxinApplyRecord toLvxinApplyRecord(ApprovalRequest approvalRequest);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "partnerUserId", target = "orderNo")
    @Mapping(source = "baseInfo.email", target = "email")
    @Mapping(source = "name", target = "name")
    @Mapping(source = "phone", target = "mobile")
    @Mapping(source = "idCard", target = "idCardNo")
    @Mapping(source = "authInfo.frontUrl", target = "idPositive")
    @Mapping(source = "authInfo.backUrl", target = "idNegative")
    @Mapping(source = "authInfo.sex", target = "idSex")
    @Mapping(source = "authInfo.nation", target = "idEthnic")
    @Mapping(source = "authInfo.authority", target = "idIssueOrg")
    @Mapping(source = "authInfo.startDueTimeOcr", target = "idStartTime")
    @Mapping(source = "authInfo.endDueTimeOcr", target = "idEndTime")
    @Mapping(source = "authInfo.address", target = "idAddress")
    @Mapping(source = "authInfo.borrower", target = "livePhoto")
    @Mapping(source = "authInfo.liveRate", target = "faceScore")
    @Mapping(source = "authInfo.facialSupplier", target = "facialSupplier")
    @Mapping(source = "baseInfo.loanPurpose", target = "loanPurpose")
    @Mapping(source = "baseInfo.educational", target = "education")
    @Mapping(source = "baseInfo.inCome", target = "monthlyIncome")
    @Mapping(source = "baseInfo.workType", target = "job")
    @Mapping(source = "baseInfo.companyName", target = "workUnitName", qualifiedByName = "toCompanyName")
    @Mapping(source = "baseInfo.companyAddress", target = "workUnitAddress")
    @Mapping(source = "baseInfo.companyProvinceCode", target = "workUnitProvinceCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.companyCityCode", target = "workUnitCityCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.companyAreaCode", target = "workUnitAreaCode", qualifiedByName = "leftStr")
    @Mapping(source = "baseInfo.maritalStatus", target = "marriage")
    @Mapping(source = "deviceInfo.gpsLng", target = "longitude")
    @Mapping(source = "deviceInfo.gpsLat", target = "latitude")
    @Mapping(source = "baseInfo.industry", target = "industry")
    @Mapping(source = "contactInfos", target = "relations", qualifiedByName = "toRelationJsonArrayStr")
    @Mapping(source = "deviceInfo", target = "deviceInfo", qualifiedByName = "toJsonStr")
    @Mapping(source = "productType", target = "isIncludingEquity", qualifiedByName = "toIncludingEquityStr")
    @Mapping(constant = "O", target = "equityRecipient")
    LvxinApplyRecord toLvxinApplyRecord(@MappingTarget LvxinApplyRecord applyRecord, ApprovalRequest approvalRequest);


    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "preOrder.openId", target = "id")
    @Mapping(source = "preOrder.certNo", target = "certNo")
    @Mapping(source = "preOrder.mobile", target = "mobile")
    @Mapping(source = "preOrder.name", target = "name")
    @Mapping(source = "applyRecord.marriage", target = "marriage")
    @Mapping(source = "applyRecord.education", target = "education", qualifiedByName = "toEducation")
    @Mapping(source = "applyRecord.monthlyIncome", target = "income")
    @Mapping(source = "applyRecord.industry", target = "industry", qualifiedByName = "toIndustry")
    @Mapping(source = "applyRecord.job", target = "position", qualifiedByName = "toPosition")
    @Mapping(source = "applyRecord.email", target = "email")
    @Mapping(source = "applyRecord.livingAddress", target = "livingAddress")
    @Mapping(source = "applyRecord.livingProvinceCode", target = "livingProvinceCode", qualifiedByName = "toProvinceCode")
    @Mapping(source = "applyRecord.livingCityCode", target = "livingCityCode", qualifiedByName = "toCityCode")
    @Mapping(source = "applyRecord.livingAreaCode", target = "livingDistrictCode", qualifiedByName = "toDistrictCode")
    @Mapping(source = "applyRecord.livingAddress", target = "livingStreet")
    @Mapping(source = "applyRecord.workUnitName", target = "unit", qualifiedByName = "toCompanyName")
    @Mapping(source = "applyRecord.workUnitAddress", target = "unitAddress")
    @Mapping(source = "applyRecord.workUnitProvinceCode", target = "unitProvinceCode", qualifiedByName = "leftStr")
    @Mapping(source = "applyRecord.workUnitCityCode", target = "unitCityCode", qualifiedByName = "leftStr")
    @Mapping(source = "applyRecord.workUnitAreaCode", target = "unitDistrictCode", qualifiedByName = "leftStr")
    @Mapping(source = "applyRecord.workUnitAddress", target = "unitStreet")
    UserInfo toUserInfo(PreOrder preOrder, LvxinApplyRecord applyRecord);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "idCardNo", target = "certNo")
    @Mapping(source = "idAddress", target = "certAddress")
    @Mapping(source = "idIssueOrg", target = "certSignOrg")
    @Mapping(source = "idCardNo", target = "provinceCode", qualifiedByName = "toProvinceCode")
    @Mapping(source = "idCardNo", target = "cityCode", qualifiedByName = "toCityCode")
    @Mapping(source = "idCardNo", target = "districtCode", qualifiedByName = "toDistrictCode")
    @Mapping(source = "idSex", target = "gender", qualifiedByName = "toGender")
    @Mapping(source = "idEthnic", target = "nation")
    UserOcr toUserOcr(LvxinApplyRecord applyRecord);


    List<ContractResponse.Contract> toContractResponseList(List<AgreementShow> agreements);

    List<LvxinBankInfo> toBankList(List<LvxinBankList> lvxinBankList);

    @Mapping(source = "customRepayDate", target = "dueTime", dateFormat = "yyyy-MM-dd")
    @Mapping(source = "customTotalAmt", target = "repayMoney")
    @Mapping(source = "principalAmt", target = "principal")
    @Mapping(source = "interestAmt", target = "interest")
    @Mapping(source = ".", target = "rightFee", qualifiedByName = "calcOtherFee")
    @Mapping(constant = "0", target = "overdueFee")
    @Mapping(constant = "0", target = "fee")
    LvxinRepayPlan toTrialRepayPlan(RepayPlanItem repayPlan);

    List<LvxinRepayPlan> toTrialRepayPlanList(List<RepayPlanItem> repayPlans);

    @Mappings({
        @Mapping(source = "repayPlan.planRepayDate", target = "dueTime", dateFormat = "yyyy-MM-dd"),
        @Mapping(source = "repayPlan.actRepayTime", target = "lastRepaymentTime", qualifiedByName = "toActRepayTime"),
        @Mapping(source = "repayPlan", target = "isOverdue", qualifiedByName = "isOverDue"),
        @Mapping(source = "repaymentStatus", target = "repaymentStatus"),
        @Mapping(source = "period", target = "period"),
        @Mapping(source = "defaultAmount", target = "repayMoney"),
        @Mapping(source = "defaultAmount", target = "principal"),
        @Mapping(source = "defaultAmount", target = "principalRepaid"),
        @Mapping(source = "defaultAmount", target = "interest"),
        @Mapping(source = "defaultAmount", target = "interestRepaid"),
        @Mapping(source = "defaultAmount", target = "overdueFee"),
        @Mapping(source = "defaultAmount", target = "overdueFeeRepaid"),
        @Mapping(source = "defaultAmount", target = "rightFee"),
        @Mapping(source = "defaultAmount", target = "rightFeeRepaid"),
        @Mapping(source = "defaultAmount", target = "fee"),
        @Mapping(source = "defaultAmount", target = "feeRepaid")
    })
    LvxinRepayPlan toDefaultRepayPlan(RepayPlan repayPlan, BigDecimal defaultAmount, Integer repaymentStatus, Integer period);

    @Mappings({
        @Mapping(source = "planRepayDate", target = "dueTime", dateFormat = "yyyy-MM-dd"),
        @Mapping(source = "actRepayTime", target = "lastRepaymentTime", qualifiedByName = "toActRepayTime"),
        @Mapping(source = ".", target = "isOverdue", qualifiedByName = "isOverDue"),
        @Mapping(source = "amount", target = "repayMoney", qualifiedByName = "safeAmount"),
        @Mapping(source = "principalAmt", target = "principal", qualifiedByName = "safeAmount"),
        @Mapping(source = "actPrincipalAmt", target = "principalRepaid", qualifiedByName = "safeAmount"),
        @Mapping(source = "interestAmt", target = "interest", qualifiedByName = "safeAmount"),
        @Mapping(source = "actInterestAmt", target = "interestRepaid", qualifiedByName = "safeAmount"),
        @Mapping(source = "penaltyAmt", target = "overdueFee", qualifiedByName = "safeAmount"),
        @Mapping(source = "actPenaltyAmt", target = "overdueFeeRepaid", qualifiedByName = "safeAmount"),
        @Mapping(constant = "0", target = "rightFee"),
        @Mapping(constant = "0", target = "rightFeeRepaid"),
        @Mapping(constant = "0", target = "fee"),
        @Mapping(constant = "0", target = "feeRepaid")
    })
    LvxinRepayPlan toRepayPlan(RepayPlan repayPlan);


    @Mappings({
        @Mapping(target = "dueTime", ignore = true),
        @Mapping(target = "lastRepaymentTime", ignore = true),
        @Mapping(target = "isOverdue", ignore = true),
        @Mapping(target = "repaymentStatus", ignore = true),
        @Mapping(source = "amount", target = "repayMoney", qualifiedByName = "safeAmount"),
        @Mapping(source = "principalAmt", target = "principal", qualifiedByName = "safeAmount"),
        @Mapping(source = "actPrincipalAmt", target = "principalRepaid", qualifiedByName = "safeAmount"),
        @Mapping(source = "interestAmt", target = "interest", qualifiedByName = "safeAmount"),
        @Mapping(source = "actInterestAmt", target = "interestRepaid", qualifiedByName = "safeAmount"),
        @Mapping(source = "penaltyAmt", target = "overdueFee", qualifiedByName = "safeAmount"),
        @Mapping(source = "actPenaltyAmt", target = "overdueFeeRepaid", qualifiedByName = "safeAmount"),
        @Mapping(constant = "0", target = "rightFee"),
        @Mapping(constant = "0", target = "rightFeeRepaid"),
        @Mapping(constant = "0", target = "fee"),
        @Mapping(constant = "0", target = "feeRepaid")
    })
    LvxinRepayPlan copyRepayPlan(@MappingTarget LvxinRepayPlan lvxinRepayPlan, RepayPlan repayPlan);

    @Mappings({
        @Mapping(source = "amount", target = "totalShouldPayAmount"),
        @Mapping(source = "principal", target = "totalCapital"),
        @Mapping(source = "interest", target = "totalInterest"),
        @Mapping(source = "penalty", target = "totalPenaltyAmout"),
        @Mapping(source = ".", target = "totalRightFeeAmount", qualifiedByName = "calcOtherFee"),
        @Mapping(constant = "0", target = "totalFeeAmount"),
        @Mapping(constant = "0", target = "totalReduceAmount")
    })
    LvxinRepayTrialResponse toRepayTrailResponse(TrialResultVo trialResultVo);

    @Mappings({
        @Mapping(source = "amount", target = "totalAmt"),
        @Mapping(source = "principal", target = "totalPrincipal"),
        @Mapping(source = "interest", target = "totalInterest"),
        @Mapping(source = "penalty", target = "totalPenaltyAmt"),
        @Mapping(source = "breachFee", target = "totalBreachAmt"),
        @Mapping(source = "consultFee", target = "totalConsultFee")
    })
    LvxinRepayTrialResponseV2 toRepayTrailResponseV2(TrialResultVo trialResultVo);

    @Mapping(source = "loanId", target = "loanId")
    @Mapping(source = "period", target = "period")
    @Mapping(source = "repayPurpose", target = "repayPurpose")
    @Mapping(constant = "ONLINE", target = "repayMode")
    @Mapping(constant = "REPAY", target = "repayType")
    @Mapping(constant = "CAPITAL", target = "paySide")
    @Mapping(constant = "USER", target = "operationSource")
    OnlineRepayApplyRequest toOnlineApplyRequest(
        LvxinRepayRequest request, String loanId, Integer period, RepayPurpose repayPurpose);


    @Mapping(target = "id", ignore = true)
    @Mapping(source = "loanGid", target = "loanId")
    @Mapping(source = "partnerUserId", target = "outCreditId")
    @Mapping(source = "repaymentGid", target = "outRepayId")
    @Mapping(source = "period", target = "periods")
    @Mapping(source = "consultationFeeWaiver", target = "consultationFeeWaiver")
    @Mapping(source = "penaltyInterestWaiver", target = "penaltyInterestWaiver")
    LvxinRepayApplyRecord toRepayApplyRecord(LvxinRepayRequest request);

    @Mapping(target = "loanGid", source = "outLoanId")
    @Mapping(target = "partnerUserId", source = "outCreditId")
    @Mapping(target = "partnerOrderNo", source = "loanId")
    @Mapping(target = "repaymentGid", source = "outRepayId")
    @Mapping(target = "period", source = "periods")
    LvxinRepayRequest toLvxinRepayRequest(LvxinRepayApplyRecord repayApplyRecord);

    @Mappings({
        @Mapping(target = "name", source = "shortName"),
        @Mapping(target = "code", source = "bankCode"),
    })
    LvxinBankInfo toBankInfo(LvxinBankList LvxinBankList);

    @Mappings({
        @Mapping(target = "url", source = "agreementUrl"),
        @Mapping(target = "name", source = "agreementName"),
    })
    ContractResponse.Contract toContractResponse(AgreementShow agreementShow);


    @Named("toGender")
    static Gender toGender(String idSex) {
        if (StringUtils.isBlank(idSex)) {
            return Gender.UNKNOWN;
        }
        return switch (idSex) {
            case "1" -> Gender.MALE;
            case "0" -> Gender.FEMALE;
            default -> Gender.UNKNOWN;
        };
    }

    @Named("toDtoMarriage")
    default String toDtoMarriage(Marriage marriage) {
        if (marriage == null) {
            return null;
        }
        return switch (marriage) {
            case UNMARRIED -> DictEnum.MaritalStatusType.UNMARRIED.getCode();
            case MARRIED -> DictEnum.MaritalStatusType.MARRIED.getCode();
            case DIVORCED -> DictEnum.MaritalStatusType.DIVORCED.getCode();
            case WIDOWED -> DictEnum.MaritalStatusType.WIDOWED.getCode();
            default -> DictEnum.RelationshipType.OTHER.getCode();
        };
    }


    @Named("toIndustry")
    default Industry toIndustry(String industryStr) {
        if (StringUtils.isBlank(industryStr)) {
            return Industry.TWENTY;
        }
        return switch (industryStr) {
            case "1" -> Industry.ONE;
            case "2" -> Industry.TWO;
            case "3" -> Industry.THREE;
            case "4" -> Industry.FOUR;
            case "5" -> Industry.FIVE;
            case "6" -> Industry.SIX;
            case "7" -> Industry.SEVEN;
            case "8" -> Industry.EIGHT;
            case "9" -> Industry.NINE;
            case "10" -> Industry.TEN;
            case "11" -> Industry.ELEVEN;
            case "12" -> Industry.TWELVE;
            case "13" -> Industry.THIRTEEN;
            case "14" -> Industry.FOURTEEN;
            case "15" -> Industry.FIFTEEN;
            case "16" -> Industry.SIXTEEN;
            case "17" -> Industry.SEVENTEEN;
            case "18" -> Industry.EIGHTEEN;
            case "19" -> Industry.NINETEEN;
            default -> Industry.TWENTY;
        };
    }

    @Named("toEducation")
    static Education toEducation(String education) {
        if (StringUtils.isBlank(education)) {
            return Education.UNKNOWN;
        }
        return switch (education) {
            case "POSTGRADUATE_AND_ABOVE" -> Education.MASTER;
            case "UNIVERSITY" -> Education.COLLEGE;
            case "SPECIAL_SCHOOL" -> Education.JUNIOR_COLLEGE;
            case "HIGH_SCHOOL" -> Education.HIGH_SCHOOL;
            case "JUNIOR_SCHOOL_AND_BELOW" -> Education.JUNIOR_HIGH_SCHOOL;
            default -> Education.UNKNOWN;
        };
    }

    @Named("toPosition")
    default Position toPosition(String jobStr) {
        DictEnum.JobCategoryType job = DictEnum.JobCategoryType.getByCode(jobStr);
        if (job == null) {
            job = DictEnum.JobCategoryType.OTHER;
        }
        return switch (job) {
            case OTHER -> Position.ELEVEN;
            case FRONT_LINE_WORKER -> Position.THIRTEEN;
            case EDUCATOR -> Position.FOURTEEN;
            case SALES_PERSON -> Position.FIFTEEN;
            case SELF_EMPLOYED -> Position.SIXTEEN;
            case PROPERTY_STAFF -> Position.SEVENTEEN;
            case SERVICE_STAFF -> Position.EIGHTEEN;
            case GOVERNMENT_EMPLOYEE -> Position.NINETEEN;
            case MEDICAL_PROFESSIONAL -> Position.TWENTY;
            case TECHNICAL_PROFESSIONAL -> Position.TWENTY_ONE;
            case MILITARY -> Position.TWENTY_TWO;
        };
    }


    @Named("toProvinceCode")
    default String toProvinceCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, 2) + "0000";
    }

    @Named("toCityCode")
    default String toCityCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, CERT_NO_CITY_LENGTH) + "00";
    }

    @Named("toDistrictCode")
    default String toDistrictCode(String certNo) {
        if (StringUtil.isBlank(certNo)) {
            return "";
        }
        return certNo.substring(0, CERT_NO_DISTRICT_LENGTH);
    }

    @Named("leftStr")
    default String leftStr(String code) {
        return StringUtils.left(code, CERT_NO_DISTRICT_LENGTH);
    }

    @Named("toRelationJsonArrayStr")
    default String toRelationJsonArrayStr(List<ContactInfo> contactInfos) {
        List<UserContactInfo> userContactInfos = new ArrayList<>();
        for (ContactInfo contactInfo : contactInfos) {
            UserContactInfo obj = new UserContactInfo();
            obj.setName(contactInfo.getName());
            obj.setPhone(contactInfo.getPhone());
            String relation = contactInfo.getRelation();
            obj.setRelation(toRelation(relation));
            userContactInfos.add(obj);
        }
        return JSON.toJSONString(userContactInfos);
    }
    //根据产品代码转换
    @Named("toIncludingEquityStr")
    default String toIncludingEquityStr(String productType) {
        return switch (productType) {
            case "01" , "03"-> "N";
            case "02" -> "Y";
            default -> null;
        };
    }
    @Named("toRelation")
    default Relation toRelation(String relation) {
        if (StringUtils.isBlank(relation)) {
            return Relation.UNKNOWN;
        }
        return switch (relation) {
            case "PARENTS" -> Relation.PARENTS;
            case "SPOUSE" -> Relation.SPOUSE;
            case "CHILDREN" -> Relation.CHILDREN;
            //兄弟姐妹
            case "SIBLING" -> Relation.SIBLING;
            case "FRIEND" -> Relation.FRIEND;
            case "COLLEAGUE" -> Relation.COLLEAGUE;
            //亲戚
            case "RELATIVE" -> Relation.RELATIVE;
            default -> Relation.UNKNOWN;
        };
    }

    @Named("toActRepayTime")
    static Long toActRepayTime(LocalDateTime actRepayTime) {
        return actRepayTime == null ? 0L : (DateUtil.toMilliseconds(actRepayTime) / THOUSAND);
    }

    @Named("isOverDue")
    static boolean isOverDue(RepayPlan repayPlan) {
        return AmountUtil.safeAmount(repayPlan.getActAmount()).compareTo(BigDecimal.ZERO) <= 0 && repayPlan.getPlanRepayDate().isBefore(LocalDate.now());
    }


    @Named("toJsonStr")
    default String toJsonStr(Object obj) {
        return JsonUtil.toJsonString(obj);
    }

    @Named("safeAmount")
    default BigDecimal safeAmount(BigDecimal amount) {
        return AmountUtil.safeAmount(amount);
    }

    @Named("calcOtherFee")
    default BigDecimal calcOtherFee(RepayPlan repayPlan) {
        return AmountUtil.sum(repayPlan.getGuaranteeAmt(), repayPlan.getConsultFee());
    }

    @Named("calcOtherFee")
    default BigDecimal calcOtherFee(TrialResultVo trialResultVo) {
        return AmountUtil.sum(trialResultVo.getGuaranteeFee(), trialResultVo.getConsultFee());
    }

    @Named("calcOtherFee")
    default BigDecimal calcOtherFee(RepayPlanItem repayPlanItem) {
        return AmountUtil.sum(repayPlanItem.getGuaranteeAmt(), repayPlanItem.getConsultAmt());
    }

    @Named("calcActOtherFee")
    default BigDecimal calcActOtherFee(RepayPlan repayPlan) {
        return AmountUtil.sum(repayPlan.getActGuaranteeAmt(), repayPlan.getActConsultFee());
    }

    @Named("toLvxinLoanStatus")
    static LvxinLoanStatus toLvxinLoanStatusByNewFlow(OrderState orderState) {
        return switch (orderState) {
            case INIT, AUDIT_PASS -> LvxinLoanStatus.INIT;
            case LOANING, CREDITING, CREDIT_PASS, SUSPENDED -> LvxinLoanStatus.LOANING;
            case LOAN_FAIL, LOAN_CANCEL, CREDIT_FAIL -> LvxinLoanStatus.LOAN_FAIL;
            case LOAN_PASS, CLEAR -> LvxinLoanStatus.LOAN_PASS;
            default -> null;
        };
    }


    @Named("toInnerPeriods")
    default String toPeriods(List<Integer> periods) {
        return periods.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    @Named("toLvxinPeriods")
    default List<Integer> toPeriods(String periods) {
        return StringUtil.isBlank(periods) ? new ArrayList<>() : Arrays.stream(periods.split(",")).map(Integer::valueOf).toList();
    }

    @Named("toBankChannel")
    default BankChannel toBankChannel(String productType) {
        return switch (productType) {
            case "01", "02" -> BankChannel.CYBK;
            case "03" -> BankChannel.HXBK;
            default -> null;
        };
    }

    public static BankChannel toBankChannelPub(String productType) {
        return switch (productType) {
            case "01", "02" -> BankChannel.CYBK;
            case "03" -> BankChannel.HXBK;
            default -> null;
        };
    }
}
