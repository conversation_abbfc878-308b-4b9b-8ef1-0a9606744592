# 融担公司多IP路由方案设计

## 📋 需求背景

系统需要支持两个融担公司（融担1、融担2）使用不同的出口IP调用资方接口：
- 融担1 使用 IP1
- 融担2 使用 IP2
- 同一个资方可能被两个融担公司都调用

## 🎯 解决方案

### 方案1：HTTP客户端工厂模式（推荐）

#### 1.1 核心思路
- 为每个融担公司创建独立的HTTP客户端实例
- 每个客户端绑定特定的出口IP地址
- 通过工厂模式根据融担公司获取对应的客户端

#### 1.2 技术实现

##### 1.2.1 IP路由配置类
```java
@Configuration
@ConfigurationProperties(prefix = "guarantee.company.ip.routing")
public class GuaranteeCompanyIpRoutingConfig {
    
    private Map<String, IpConfig> companies = new HashMap<>();
    
    @Data
    public static class IpConfig {
        private String outboundIp;
        private String description;
    }
    
    public IpConfig getIpConfig(GuaranteeCompany company) {
        return companies.get(company.name());
    }
}
```

##### 1.2.2 HTTP客户端工厂
```java
@Component
public class GuaranteeCompanyHttpClientFactory {
    
    private final Map<GuaranteeCompany, CloseableHttpClient> httpClientMap = new ConcurrentHashMap<>();
    private final GuaranteeCompanyIpRoutingConfig routingConfig;
    
    @PostConstruct
    public void initHttpClients() {
        for (GuaranteeCompany company : GuaranteeCompany.values()) {
            IpConfig ipConfig = routingConfig.getIpConfig(company);
            if (ipConfig != null) {
                CloseableHttpClient client = createHttpClientWithIpBinding(ipConfig.getOutboundIp());
                httpClientMap.put(company, client);
            }
        }
    }
    
    private CloseableHttpClient createHttpClientWithIpBinding(String outboundIp) {
        try {
            InetAddress localAddress = InetAddress.getByName(outboundIp);
            
            return HttpClients.custom()
                .setConnectionManager(createConnectionManager(localAddress))
                .setDefaultRequestConfig(RequestConfig.custom()
                    .setConnectTimeout(30000)
                    .setSocketTimeout(60000)
                    .build())
                .build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create HTTP client with IP binding: " + outboundIp, e);
        }
    }
    
    private HttpClientConnectionManager createConnectionManager(InetAddress localAddress) {
        PoolingHttpClientConnectionManager connectionManager = 
            new PoolingHttpClientConnectionManager(
                RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", new PlainConnectionSocketFactory() {
                        @Override
                        public Socket createSocket(HttpContext context) throws IOException {
                            Socket socket = new Socket();
                            socket.bind(new InetSocketAddress(localAddress, 0));
                            return socket;
                        }
                    })
                    .register("https", new SSLConnectionSocketFactory(SSLContexts.createDefault()) {
                        @Override
                        public Socket createSocket(HttpContext context) throws IOException {
                            Socket socket = super.createSocket(context);
                            socket.bind(new InetSocketAddress(localAddress, 0));
                            return socket;
                        }
                    })
                    .build()
            );
        
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(50);
        return connectionManager;
    }
    
    public CloseableHttpClient getHttpClient(GuaranteeCompany company) {
        CloseableHttpClient client = httpClientMap.get(company);
        if (client == null) {
            throw new IllegalArgumentException("No HTTP client configured for company: " + company);
        }
        return client;
    }
}
```

##### 1.2.3 资方请求服务基类
```java
@Component
public abstract class BaseCapitalRequestService {
    
    @Autowired
    protected GuaranteeCompanyHttpClientFactory httpClientFactory;
    
    protected <T> T executeRequest(GuaranteeCompany company, String url, Object request, Class<T> responseClass) {
        CloseableHttpClient httpClient = httpClientFactory.getHttpClient(company);
        
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setEntity(new StringEntity(JsonUtil.convertToString(request), "UTF-8"));
            
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseStr = EntityUtils.toString(response.getEntity(), "UTF-8");
                return JsonUtil.convertToObject(responseStr, responseClass);
            }
        } catch (Exception e) {
            throw new RuntimeException("Request failed for company: " + company, e);
        }
    }
}
```

#### 1.3 配置示例
```yaml
guarantee:
  company:
    ip:
      routing:
        companies:
          CJRD:
            outbound-ip: "*************"  # 融担1的IP
            description: "超捷融担出口IP"
          JKRD:
            outbound-ip: "*************"  # 融担2的IP
            description: "科融融担出口IP"
```

### 方案2：网络层路由配置（运维方案）

#### 2.1 核心思路
- 在操作系统层面配置路由规则
- 应用层通过特定的网络接口发送请求
- 需要运维配合配置网络路由

#### 2.2 实现方式
```bash
# 为不同的融担公司配置不同的路由表
# 融担1使用eth0网卡（IP1）
ip route add [资方IP段] dev eth0 src *************

# 融担2使用eth1网卡（IP2）  
ip route add [资方IP段] dev eth1 src *************
```

### 方案3：代理服务器方案

#### 3.1 核心思路
- 部署两个代理服务器，分别绑定不同的出口IP
- 应用根据融担公司选择不同的代理服务器

#### 3.2 实现方式
```java
// 为不同融担公司配置不同的代理
HttpHost proxy1 = new HttpHost("proxy1.company.com", 8080);  // 融担1代理
HttpHost proxy2 = new HttpHost("proxy2.company.com", 8080);  // 融担2代理

RequestConfig config = RequestConfig.custom()
    .setProxy(getProxyByCompany(company))
    .build();
```

## 🏆 推荐方案对比

| 方案 | 优点 | 缺点 | 复杂度 | 推荐度 |
|------|------|------|--------|--------|
| HTTP客户端工厂 | 应用层控制，灵活性高，易于测试 | 需要修改代码 | 中等 | ⭐⭐⭐⭐⭐ |
| 网络层路由 | 对应用透明，性能好 | 需要运维配合，调试困难 | 高 | ⭐⭐⭐ |
| 代理服务器 | 实现简单 | 增加网络跳转，性能损耗 | 低 | ⭐⭐ |

## 📝 实施建议

1. **推荐使用方案1（HTTP客户端工厂模式）**，原因：
   - 完全在应用层控制，便于调试和监控
   - 可以灵活配置每个融担公司的网络参数
   - 易于单元测试和集成测试
   - 对现有代码改动相对较小

2. **实施步骤**：
   - 先实现HTTP客户端工厂和配置类
   - 逐步改造现有的资方接口调用代码
   - 添加监控和日志，确保IP路由正确
   - 在测试环境验证后再上生产

3. **注意事项**：
   - 确保服务器有多个网卡或IP地址
   - 配置防火墙规则允许对应IP的出站连接
   - 添加健康检查确保IP路由正常工作
   - 考虑连接池的管理和资源释放

## 🔧 后续扩展

- 支持动态IP路由配置更新
- 添加IP路由的监控和告警
- 支持更多融担公司的扩展
- 集成到现有的配置管理系统
